import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameZingToConvertEcom1715518800000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    // Rename zingDiscounts column to convertEcomDiscounts in order table
    await queryRunner.query(`
      ALTER TABLE "order" RENAME COLUMN "zingDiscounts" TO "convertEcomDiscounts";
    `);

    // Update onboardingStatus in tenant table
    await queryRunner.query(`
      UPDATE "tenant" 
      SET "onboardingStatus" = jsonb_set(
        "onboardingStatus"::jsonb, 
        '{isZingActivated}', 
        'null'::jsonb
      ) || 
      jsonb_build_object('isConvertEcomActivated', 
        CASE 
          WHEN "onboardingStatus"->>'isZingActivated' = 'true' THEN true
          ELSE false
        END
      )::jsonb
      WHERE "onboardingStatus"->>'isZingActivated' IS NOT NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    // Revert convertEcomDiscounts column back to zingDiscounts in order table
    await queryRunner.query(`
      ALTER TABLE "order" RENAME COLUMN "convertEcomDiscounts" TO "zingDiscounts";
    `);

    // Revert onboardingStatus in tenant table
    await queryRunner.query(`
      UPDATE "tenant" 
      SET "onboardingStatus" = jsonb_set(
        "onboardingStatus"::jsonb, 
        '{isConvertEcomActivated}', 
        'null'::jsonb
      ) || 
      jsonb_build_object('isZingActivated', 
        CASE 
          WHEN "onboardingStatus"->>'isConvertEcomActivated' = 'true' THEN true
          ELSE false
        END
      )::jsonb
      WHERE "onboardingStatus"->>'isConvertEcomActivated' IS NOT NULL;
    `);
  }
}
