import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Tenant } from './tenant.entity';
import { TenantService } from './tenant.service';
import { TenantController } from './tenant.controller';
import { ApiSyncQueueModule } from '../api-sync-queue/api-sync-queue.module';
import { CouponModule } from '../coupon/coupon.module';
import { AuditLogModule } from '../audit-log/audit-log.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Tenant]),
    ApiSyncQueueModule,
    CouponModule,
    AuditLogModule,
  ],
  providers: [TenantService],
  exports: [TenantService, TypeOrmModule],
  controllers: [TenantController],
})
export class TenantModule {}
