import {
  CacheModule,
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { parse } from 'pg-connection-string';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user.module';
import { CommentModule } from './comment/comment.module';
import { TenantModule } from './tenant/tenant.module';
import { CouponModule } from './coupon/coupon.module';
import { AuthMiddleware } from './auth/auth.middleware';
import { ApiSyncQueueModule } from './api-sync-queue/api-sync-queue.module';
import { OptinModule } from './optin/optin.module';
import { StatisticModule } from './statistic/statistic.module';
import { BillingModule } from './billing/billing.module';
import { APP_GUARD } from '@nestjs/core';
import { RolesGuard } from './auth/guards/roles.guard';
import { UserController } from './user/user.controller';
import { CouponController } from './coupon/coupon.controller';
import { TenantController } from './tenant/tenant.controller';
import { StatisticController } from './statistic/statistic.controller';
import { OptinController } from './optin/optin.controller';
import { BillingController } from './billing/controllers/billing.controller';
import { ImpressionModule } from './impression/impression.module';
import { EmailModule } from './email/email.module';
import { BillingSettingsController } from './billing/controllers/billing-settings.controller';
import { CreditController } from './billing/controllers/credit.controller';
import { EmailController } from './email/email.controller';
import { IntegrationModule } from '@app/integration/integration.module';
import { IntegrationController } from '@app/integration/controllers/integration/integration.controller';
import { IntegrationPlatformName } from '@app/integration/configuration/integrations';
import { IntegrationPlatformController } from '@app/integration/controllers/integration-platform/integration-platform.controller';
import { ScheduleModule } from '@nestjs/schedule';
import { CouponReconciliationController } from './coupon/coupon-reconciliation.controller';
import { OrderReconciliationController } from './order/order-reconciliation.controller';
import { OrderModule } from './order/order.module';
import { TenantBillingController } from './billing/controllers/tenant-billing.controller';
import { AppController } from './app.controller';

let connectionConfig;

if (process.env.DATABASE_URL) {
  const connection = parse(process.env.DATABASE_URL);
  console.log(connection)
  connectionConfig = {
    type: process.env.TYPEORM_CONNECTION,
    host: connection.host,
    port: connection.port,
    username: connection.user,
    password: connection.password,
    database: connection.database,
    entities: [process.env.TYPEORM_ENTITIES],
    synchronize: process.env.TYPEORM_SYNCHRONIZE === 'true',
    migrationsRun: process.env.TYPEORM_MIGRATIONS_RUN === 'true',
    migrations: [process.env.TYPEORM_MIGRATIONS],
    cli: {
      migrationsDir: process.env.TYPEORM_MIGRATIONS_DIR,
    },
    extra: {
      /*
       rejectUnauthorized explicitly needed here because of updates in node 12.22.5, see node changelog for deets
       https://github.com/nodejs/node/blob/master/doc/changelogs/CHANGELOG_V12.md#2021-08-11-version-12225-erbium-lts-bethgriggs
       */
      ssl:
        typeof process.env.TYPEORM_SSL === 'undefined' ||
          process.env.TYPEORM_SSL === 'true'
          ? { rejectUnauthorized: false }
          : false,
    },
    logging: process.env.TYPEORM_LOGGING === 'true',
  };
}

@Module({
  imports: [
    TypeOrmModule.forRoot(connectionConfig),
    AuthModule,
    CommentModule,
    TenantModule,
    UserModule,
    CouponModule,
    ApiSyncQueueModule,
    OptinModule,
    StatisticModule,
    BillingModule,
    ImpressionModule,
    EmailModule,
    CacheModule.register(),
    IntegrationModule,
    ScheduleModule.forRoot(),
    OrderModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .exclude({
        path: 'billing/setup/shopify/redirect',
        method: RequestMethod.GET,
      })
      .forRoutes(
        UserController,
        CouponController,
        TenantController,
        StatisticController,
        OptinController,
        BillingController,
        BillingSettingsController,
        CreditController,
        EmailController,
        IntegrationController,
        IntegrationPlatformController,
        CouponReconciliationController,
        OrderReconciliationController,
        TenantBillingController,
      );
  }
}
