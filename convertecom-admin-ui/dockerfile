# Use the official Node.js image from AWS ECR Public Gallery to avoid Docker Hub rate limits
FROM public.ecr.aws/docker/library/node:18

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json files to the container
COPY package.json package-lock.json ./

# Install dependencies
RUN npm ci

# Copy the rest of the application code to the container
COPY . .

# Build the React application
RUN npm run build

# Expose the port the app runs on (production server uses 8080)
EXPOSE 8080

# Command to run the application in production mode
CMD ["npm", "run", "start:prod"]

